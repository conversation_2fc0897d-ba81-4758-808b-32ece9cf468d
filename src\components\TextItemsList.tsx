import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Type, Eye, EyeOff, Trash2 } from 'lucide-react';

interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  visible?: boolean;
}

interface TextItemsListProps {
  textItems: TextItem[];
  selectedItemId?: string;
  onSelectItem: (id: string) => void;
  onToggleVisibility: (id: string) => void;
  onDeleteItem: (id: string) => void;
}

export const TextItemsList: React.FC<TextItemsListProps> = ({
  textItems,
  selectedItemId,
  onSelectItem,
  onToggleVisibility,
  onDeleteItem,
}) => {
  return (
    <Card className="bg-card border-border">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Type className="h-4 w-4" />
          Text Items ({textItems.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {textItems.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No text items added yet
          </p>
        ) : (
          textItems.map((item) => (
            <div
              key={item.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedItemId === item.id
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:bg-muted/50'
              }`}
              onClick={() => onSelectItem(item.id)}
            >
              <div className="flex items-center justify-between gap-2">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {item.text || 'Empty text'}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="secondary" className="text-xs">
                      {item.fontFamily}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {item.fontSize}px
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleVisibility(item.id);
                    }}
                  >
                    {item.visible !== false ? (
                      <Eye className="h-3 w-3" />
                    ) : (
                      <EyeOff className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteItem(item.id);
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
};