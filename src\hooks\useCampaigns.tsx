import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";

import { toast } from "sonner";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from "@/contexts/AuthContext";

export interface TextOverlayProject {
  id?: string;
  title: string;
  image_url: string;
  image_bucket_path?: string;
  text_objects: any[];
  canvas_settings?: any;
  provider?: string;
  author_name?: string;
  author_username?: string;
  author_profile_url?: string;
  image_source_id?: string;
  image_source_url?: string;
  image_source_tags?: string[];
  downloaded_at?: string;
}

export const useCampaigns = () => {
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(false);
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: projects = [],
    isLoading,
    error,
    isError,
  } = useQuery({
    queryKey: ["campaigns", user?.id],
    queryFn: async () => {
      if (!user) {
        console.log("No user found, returning empty notebooks array");
        return [];
      }

      console.log("Fetching notebooks for user:", user.id);

      // First get the notebooks
      const { data, error } = await supabase
        .from("text_overlay_projects")
        .select("*")
        .eq("user_id", user.id)
        .order("updated_at", { ascending: false });

      if (error) {
        console.error("Error fetching notebooks:", error);
        throw error;
      }

      return data;

    },
    enabled: isAuthenticated && !authLoading,
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error?.message?.includes("JWT") || error?.message?.includes("auth")) {
        return false;
      }
      return failureCount < 3;
    },
  });


  const createCampaign = useMutation({
    mutationFn: async (notebookData: { title: string; description?: string }) => {
      console.log('Creating notebook with data:', notebookData);
      console.log('Current user:', user?.id);
      
      if (!user) {
        console.error('User not authenticated');
        throw new Error('User not authenticated');
      }
      const projectData = {
        user_id: user.id,
        title: notebookData.title,
        image_url: "",
        image_bucket_path: "",
        text_objects: ""
      };
      const { data, error } = await supabase
        .from('text_overlay_projects')
        .insert(projectData)
        .select()
        .single();

      if (error) {
        console.error('Error creating campaign:', error);
        throw error;
      }
      
      console.log('Campaign created successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('Mutation success, invalidating queries');
      queryClient.invalidateQueries({ queryKey: ['campaigns', user?.id] });
    },
    onError: (error) => {
      console.error('Mutation error:', error);
    },
  });


  const uploadImage = async (
    file: File
  ): Promise<{ url: string; path: string } | null> => {
    if (!user) return null;

    try {
      const fileExt = file.name.split(".").pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from("public-images")
        .upload(fileName, file);

      if (uploadError) {
        console.error("Upload error:", uploadError);
        toast.error("Failed to upload image");
        return null;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from("public-images").getPublicUrl(fileName);

      return { url: publicUrl, path: fileName };
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image");
      return null;
    }
  };

  const saveProject = async (
    project: TextOverlayProject,
    imageFile?: File
  ): Promise<string | null> => {
    if (!user) {
      toast.error("You must be logged in to save projects");
      return null;
    }

    setSaving(true);
    try {
      let imageUrl = project.image_url;
      let imageBucketPath = project.image_bucket_path;

      // Upload new image if provided
      if (imageFile) {
        const uploadResult = await uploadImage(imageFile);
        if (!uploadResult) {
          setSaving(false);
          return null;
        }
        imageUrl = uploadResult.url;
        imageBucketPath = uploadResult.path;
      }

      const projectData = {
        user_id: user.id,
        title: project.title,
        image_url: imageUrl,
        image_bucket_path: imageBucketPath,
        text_objects: project.text_objects,
        canvas_settings: project.canvas_settings || {},
        provider: project.provider || "upload",
        author_name: project.author_name,
        author_username: project.author_username,
        author_profile_url: project.author_profile_url,
        image_source_id: project.image_source_id,
        image_source_url: project.image_source_url,
        image_source_tags: project.image_source_tags,
        downloaded_at: project.downloaded_at,
      };

      if (project.id) {
        // Update existing project
        const { error } = await supabase
          .from("text_overlay_projects")
          .update(projectData)
          .eq("id", project.id);

        if (error) {
          console.error("Update error:", error);
          toast.error("Failed to update project");
          return null;
        }

        toast.success("Project updated successfully");
        return project.id;
      } else {
        // Create new project
        const { data, error } = await supabase
          .from("text_overlay_projects")
          .insert(projectData)
          .select("id")
          .single();

        if (error) {
          console.error("Save error:", error);
          toast.error("Failed to save project");
          return null;
        }

        toast.success("Project saved successfully");
        return data.id;
      }
    } catch (error) {
      console.error("Error saving project:", error);
      toast.error("Failed to save project");
      return null;
    } finally {
      setSaving(false);
    }
  };

  const loadProject = async (
    id: string
  ): Promise<TextOverlayProject | null> => {
    if (!user) return null;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("text_overlay_projects")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Load error:", error);
        toast.error("Failed to load project");
        return null;
      }

      return {
        id: data.id,
        title: data.title,
        image_url: data.image_url,
        image_bucket_path: data.image_bucket_path,
        text_objects: Array.isArray(data.text_objects) ? data.text_objects : [],
        canvas_settings: data.canvas_settings || {},
        provider: data.provider,
        author_name: data.author_name,
        author_username: data.author_username,
        author_profile_url: data.author_profile_url,
        image_source_id: data.image_source_id,
        image_source_url: data.image_source_url,
        image_source_tags: data.image_source_tags,
        downloaded_at: data.downloaded_at,
      };
    } catch (error) {
      console.error("Error loading project:", error);
      toast.error("Failed to load project");
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getUserProjects = async () => {
    if (!user) return [];

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("text_overlay_projects")
        .select("id, title, image_url, created_at, updated_at")
        .order("updated_at", { ascending: false });

      if (error) {
        console.error("Load projects error:", error);
        toast.error("Failed to load projects");
        return [];
      }

      return data;
    } catch (error) {
      console.error("Error loading projects:", error);
      toast.error("Failed to load projects");
      return [];
    } finally {
      setLoading(false);
    }
  };

  const deleteProject = async (projectId: string): Promise<boolean> => {
    if (!user) {
      toast.error("You must be logged in to delete projects");
      return false;
    }

    setLoading(true);
    try {
      // First get the project to find the image bucket path and provider
      const { data: project, error: fetchError } = await supabase
        .from("text_overlay_projects")
        .select("image_bucket_path, provider")
        .eq("id", projectId)
        .single();

      if (fetchError) {
        console.error("Error fetching project:", fetchError);
        toast.error("Failed to fetch project details");
        return false;
      }

      // Delete the image from storage only if it's an uploaded file (not Unsplash)
      if (project.image_bucket_path && project.provider !== "unsplash") {
        const { error: storageError } = await supabase.storage
          .from("public-images")
          .remove([project.image_bucket_path]);

        if (storageError) {
          console.error("Error deleting image from storage:", storageError);
          // Continue with project deletion even if image deletion fails
          toast.error("Warning: Could not delete associated image");
        }
      }

      // Delete the project from database
      const { error: deleteError } = await supabase
        .from("text_overlay_projects")
        .delete()
        .eq("id", projectId);

      if (deleteError) {
        console.error("Delete error:", deleteError);
        toast.error("Failed to delete project");
        return false;
      }

      toast.success("Project deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting project:", error);
      toast.error("Failed to delete project");
      return false;
    } finally {
      setLoading(false);
    }
  };
  //  notebooks,
  //     isLoading: authLoading || isLoading,
  //     error: error?.message || null,
  //     isError,
  //     createNotebook: createNotebook.mutate,
  //     isCreating: createNotebook.isPending,
  return {
    saving,
    loading,
    saveProject,
    loadProject,
    getUserProjects,
    uploadImage,
    deleteProject,
    projects,
    isLoading: authLoading || isLoading,
    error: error?.message || null,
    isError,
    createCampaign: createCampaign.mutate,
    isCreating: createCampaign.isPending,
  };
};
