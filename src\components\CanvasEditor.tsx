import { useEffect, useRef, useState, useCallback } from "react";
import { Canvas as FabricCanvas, FabricText, FabricImage, Shadow } from "fabric";
import { toast } from "sonner";
import { TextOptions } from "./TextEditor";

interface CanvasEditorProps {
  imageUrl?: string;
  onAddText: (addTextFunction: (textOptions: TextOptions) => void) => void;
  onUpdateText: (updateTextFunction: (textOptions: TextOptions) => void) => void;
  onDeleteText: (deleteTextFunction: () => void) => void;
  onTextSelected?: (textOptions: TextOptions | null) => void;
  onGetCanvasData: (getCanvasDataFunction: () => any) => void;
  loadedProjectData?: any;
  onProjectDataLoaded?: () => void;
}

export const CanvasEditor = ({ imageUrl, onAddText, onUpdateText, onDeleteText, onTextSelected, onGetCanvasData, loadedProjectData, onProjectDataLoaded }: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<FabricCanvas | null>(null);
  const [selectedTextObject, setSelectedTextObject] = useState<FabricText | null>(null);

  // Handle text selection changes
  const handleSelectionChange = useCallback((selectedObject: any) => {
    if (selectedObject && selectedObject instanceof FabricText) {
      setSelectedTextObject(selectedObject);

      if (onTextSelected) {
        // Extract current properties from the selected text object
        const textOptions: TextOptions = {
          text: selectedObject.text || '',
          fontFamily: selectedObject.fontFamily || 'Arial',
          fontSize: selectedObject.fontSize || 32,
          fontWeight: String(selectedObject.fontWeight || 'normal'),
          fontStyle: selectedObject.fontStyle || 'normal',
          textDecoration: (selectedObject as any).textDecoration || 'normal',
          fill: selectedObject.fill as string || '#000000',
          stroke: selectedObject.stroke as string,
          strokeWidth: selectedObject.strokeWidth || 0,
          shadow: selectedObject.shadow ? {
            blur: selectedObject.shadow.blur || 0,
            offsetX: selectedObject.shadow.offsetX || 0,
            offsetY: selectedObject.shadow.offsetY || 0,
            color: selectedObject.shadow.color || '#000000'
          } : undefined
        };

        console.log("CanvasEditor: Text selected with properties:", textOptions);
        onTextSelected(textOptions);
      }
    } else {
      setSelectedTextObject(null);
      if (onTextSelected) {
        console.log("CanvasEditor: Selection cleared or non-text object selected");
        onTextSelected(null);
      }
    }
  }, [onTextSelected]);

  useEffect(() => {
    console.log("CanvasEditor: Canvas initialization useEffect, canvasRef.current:", !!canvasRef.current);
    if (!canvasRef.current) {
      console.log("CanvasEditor: canvasRef.current is null, returning");
      return;
    }

    try {
      console.log("CanvasEditor: Creating FabricCanvas");
      const canvas = new FabricCanvas(canvasRef.current, {
        width: 800,
        height: 600,
        backgroundColor: "#f8f9fa",
      });

      console.log("CanvasEditor: FabricCanvas created successfully:", !!canvas);

      // Add selection event listeners
      canvas.on('selection:created', (e) => {
        handleSelectionChange(e.selected?.[0]);
      });

      canvas.on('selection:updated', (e) => {
        handleSelectionChange(e.selected?.[0]);
      });

      canvas.on('selection:cleared', () => {
        handleSelectionChange(null);
      });

      setFabricCanvas(canvas);

      return () => {
        console.log("CanvasEditor: Disposing canvas");
        canvas.dispose();
      };
    } catch (error) {
      console.error("CanvasEditor: Error creating FabricCanvas:", error);
    }
  }, [handleSelectionChange]);

  useEffect(() => {
    if (!fabricCanvas || !imageUrl) return;

    // Load image with CORS enabled to allow canvas export
    FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' })
      .then((img) => {
        // Clear existing objects
        fabricCanvas.clear();

        // Scale image to fit canvas while maintaining aspect ratio
        const canvasWidth = fabricCanvas.width || 800;
        const canvasHeight = fabricCanvas.height || 600;

        const imgWidth = img.width || 1;
        const imgHeight = img.height || 1;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        img.scale(scale);

        // Center the image manually
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2
        });
        img.setCoords();

        // Add a visible border and shadow around the image to help distinguish boundaries
        img.set({
          stroke: '#64748b', // Darker gray border color for better visibility
          strokeWidth: 3,     // 3px border width for clear visibility
          shadow: new Shadow({
            color: 'rgba(0, 0, 0, 0.2)', // More visible shadow
            blur: 10,                      // Slightly more blur for better definition
            offsetX: 0,                    // No horizontal offset
            offsetY: 3,                    // Slightly more vertical offset for depth
          }),
        });

        // Make image non-selectable and non-movable
        img.selectable = false;
        img.evented = false;

        fabricCanvas.add(img);
        fabricCanvas.sendObjectToBack(img);
        fabricCanvas.renderAll();

        toast.success("Image loaded successfully!");
      })
      .catch((error) => {
        console.error("Image loading error:", error);
        toast.error("Failed to load image. This might be due to CORS restrictions.");
      });
  }, [fabricCanvas, imageUrl]);

  const addTextToCanvas = useCallback((textOptions: TextOptions) => {
    console.log("CanvasEditor: addTextToCanvas called with:", textOptions);
    if (!fabricCanvas || !textOptions || !textOptions.text) {
      console.log("CanvasEditor: Early return - fabricCanvas:", !!fabricCanvas, "textOptions:", !!textOptions, "text:", textOptions?.text);
      return;
    }

    const fabricText = new FabricText(textOptions.text, {
      left: 100,
      top: 100,
      fontFamily: textOptions.fontFamily,
      fontSize: textOptions.fontSize,
      fontWeight: textOptions.fontWeight,
      fontStyle: textOptions.fontStyle,
      textDecoration: textOptions.textDecoration,
      fill: textOptions.fill,
      stroke: textOptions.stroke,
      strokeWidth: textOptions.strokeWidth || 0,
      shadow: textOptions.shadow ? new Shadow({
        color: textOptions.shadow.color,
        blur: textOptions.shadow.blur,
        offsetX: textOptions.shadow.offsetX,
        offsetY: textOptions.shadow.offsetY,
      }) : undefined,
    });

    console.log("CanvasEditor: Text object created:", fabricText);
    console.log("CanvasEditor: Canvas objects count before add:", fabricCanvas.getObjects().length);

    fabricCanvas.add(fabricText);
    fabricCanvas.setActiveObject(fabricText);
    fabricCanvas.renderAll();

    console.log("CanvasEditor: Canvas objects count after add:", fabricCanvas.getObjects().length);
    console.log("CanvasEditor: Canvas objects:", fabricCanvas.getObjects());

    toast.success("Text added to canvas!");
  }, [fabricCanvas]);

  const updateTextOnCanvas = useCallback((textOptions: TextOptions) => {
    console.log("CanvasEditor: updateTextOnCanvas called with:", textOptions);
    if (!fabricCanvas || !selectedTextObject || !textOptions || !textOptions.text) {
      console.log("CanvasEditor: Cannot update - fabricCanvas:", !!fabricCanvas, "selectedTextObject:", !!selectedTextObject, "textOptions:", !!textOptions);
      return;
    }

    // Update the selected text object properties
    selectedTextObject.set({
      text: textOptions.text,
      fontFamily: textOptions.fontFamily,
      fontSize: textOptions.fontSize,
      fontWeight: textOptions.fontWeight,
      fontStyle: textOptions.fontStyle,
      textDecoration: textOptions.textDecoration,
      fill: textOptions.fill,
      stroke: textOptions.stroke,
      strokeWidth: textOptions.strokeWidth || 0,
      shadow: textOptions.shadow ? new Shadow({
        color: textOptions.shadow.color,
        blur: textOptions.shadow.blur,
        offsetX: textOptions.shadow.offsetX,
        offsetY: textOptions.shadow.offsetY,
      }) : null,
    });

    fabricCanvas.renderAll();
    console.log("CanvasEditor: Text updated successfully");
    toast.success("Text updated!");
  }, [fabricCanvas, selectedTextObject]);

  const deleteSelectedText = useCallback(() => {
    if (!fabricCanvas || !selectedTextObject) {
      console.log("CanvasEditor: Cannot delete - fabricCanvas:", !!fabricCanvas, "selectedTextObject:", !!selectedTextObject);
      return;
    }

    fabricCanvas.remove(selectedTextObject);
    fabricCanvas.renderAll();
    setSelectedTextObject(null);
    console.log("CanvasEditor: Text deleted successfully");
    toast.success("Text deleted!");
  }, [fabricCanvas, selectedTextObject]);

  // Expose addTextToCanvas, updateTextOnCanvas, and deleteSelectedText through the props
  useEffect(() => {
    console.log("CanvasEditor: useEffect for onAddText, fabricCanvas:", !!fabricCanvas);
    console.log("CanvasEditor: onAddText prop:", !!onAddText);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up addTextToCanvas function");
      onAddText(addTextToCanvas);
      console.log("CanvasEditor: addTextToCanvas function passed to parent");
    }
  }, [fabricCanvas, onAddText, addTextToCanvas]);

  useEffect(() => {
    console.log("CanvasEditor: useEffect for onUpdateText, fabricCanvas:", !!fabricCanvas);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up updateTextOnCanvas function");
      onUpdateText(updateTextOnCanvas);
      console.log("CanvasEditor: updateTextOnCanvas function passed to parent");
    }
  }, [fabricCanvas, onUpdateText, updateTextOnCanvas]);

  useEffect(() => {
    console.log("CanvasEditor: useEffect for onDeleteText, fabricCanvas:", !!fabricCanvas);
    if (fabricCanvas) {
      console.log("CanvasEditor: Setting up deleteSelectedText function");
      onDeleteText(deleteSelectedText);
      console.log("CanvasEditor: deleteSelectedText function passed to parent");
    }
  }, [fabricCanvas, onDeleteText, deleteSelectedText]);

  const getCanvasData = useCallback(() => {
    if (!fabricCanvas) return { textObjects: [], canvasSettings: {} };

    const objects = fabricCanvas.getObjects();
    const textObjects = objects
      .filter(obj => obj.type === 'text')
      .map(obj => ({
        text: (obj as FabricText).text,
        left: obj.left,
        top: obj.top,
        fontSize: (obj as FabricText).fontSize,
        fontFamily: (obj as FabricText).fontFamily,
        fill: obj.fill,
        fontWeight: (obj as FabricText).fontWeight,
        fontStyle: (obj as FabricText).fontStyle,
        textDecoration: (obj as any).textDecoration,
        stroke: obj.stroke,
        strokeWidth: obj.strokeWidth,
        shadow: obj.shadow ? {
          blur: obj.shadow.blur,
          offsetX: obj.shadow.offsetX,
          offsetY: obj.shadow.offsetY,
          color: obj.shadow.color
        } : undefined
      }));

    return {
      textObjects,
      canvasSettings: {
        width: fabricCanvas.width,
        height: fabricCanvas.height
      }
    };
  }, [fabricCanvas]);

  useEffect(() => {
    if (fabricCanvas) {
      onGetCanvasData(getCanvasData);
    }
  }, [fabricCanvas, onGetCanvasData, getCanvasData]);

  // Load project data when provided
  useEffect(() => {
    if (!fabricCanvas || !loadedProjectData) return;

    try {
      // Clear existing text objects (keep background image)
      const objects = fabricCanvas.getObjects();
      objects.forEach((obj) => {
        if (obj instanceof FabricText) {
          fabricCanvas.remove(obj);
        }
      });

      // Load text objects from saved project
      if (loadedProjectData.text_objects && Array.isArray(loadedProjectData.text_objects)) {
        loadedProjectData.text_objects.forEach((textData: any) => {
          const fabricText = new FabricText(textData.text || '', {
            left: textData.left || 100,
            top: textData.top || 100,
            fontFamily: textData.fontFamily || 'Arial',
            fontSize: textData.fontSize || 32,
            fontWeight: textData.fontWeight || 'normal',
            fontStyle: textData.fontStyle || 'normal',
            textDecoration: textData.textDecoration || 'normal',
            fill: textData.fill || '#000000',
            stroke: textData.stroke,
            strokeWidth: textData.strokeWidth || 0,
            shadow: textData.shadow ? new Shadow({
              color: textData.shadow.color || '#000000',
              blur: textData.shadow.blur || 0,
              offsetX: textData.shadow.offsetX || 0,
              offsetY: textData.shadow.offsetY || 0,
            }) : undefined,
          });

          fabricCanvas.add(fabricText);
        });
      }

      fabricCanvas.renderAll();
      toast.success('Project loaded successfully!');
      
      // Notify parent that project data has been loaded
      if (onProjectDataLoaded) {
        onProjectDataLoaded();
      }
    } catch (error) {
      console.error('Error loading project data:', error);
      toast.error('Failed to load project data');
    }
  }, [fabricCanvas, loadedProjectData, onProjectDataLoaded]);

  const exportCanvas = (format: 'png' | 'jpeg' = 'png') => {
    if (!fabricCanvas) return;

    try {
      const dataURL = fabricCanvas.toDataURL({
        format,
        quality: 1,
        multiplier: 2, // Higher resolution
      });

      // Check if the dataURL is valid (not tainted by CORS)
      if (dataURL === 'data:,') {
        toast.error("Cannot export image due to CORS restrictions. Try using a different image source.");
        return;
      }

      // Create download link
      const link = document.createElement('a');
      link.download = `text-overlay-${Date.now()}.${format}`;
      link.href = dataURL;
      link.click();

      toast.success("Image exported successfully!");
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export image. This might be due to CORS restrictions with the image source.");
    }
  };

  const clearCanvas = () => {
    if (!fabricCanvas) return;
    
    // Only remove text objects, keep the background image
    const objects = fabricCanvas.getObjects();
    objects.forEach((obj) => {
      if (obj instanceof FabricText) {
        fabricCanvas.remove(obj);
      }
    });
    
    fabricCanvas.renderAll();
    toast.success("Text elements cleared!");
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Canvas</h3>
        <div className="flex gap-2">
          <button
            onClick={clearCanvas}
            className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded hover:bg-secondary/80 transition-colors"
          >
            Clear Text
          </button>
          <button
            onClick={() => exportCanvas('png')}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
          >
            Export PNG
          </button>
          <button
            onClick={() => exportCanvas('jpeg')}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
          >
            Export JPG
          </button>
        </div>
      </div>
      
      <div className="border-2 border-canvas-border rounded-lg overflow-hidden bg-canvas-bg shadow-panel">
        <canvas 
          ref={canvasRef} 
          className="max-w-full block"
        />
      </div>
      
      {!imageUrl && (
        <div className="text-center text-muted-foreground py-8">
          Upload an image to start adding text overlays
        </div>
      )}
    </div>
  );
};
